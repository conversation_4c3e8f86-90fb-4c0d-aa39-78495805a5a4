{"name": "time-tracker", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "jest --bail=1", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest --testPathIgnorePatterns=src/__tests__/integration/", "test:integration": "jest --testPathPattern=src/__tests__/integration/", "test:all": "jest --coverage --verbose"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.4.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-store": "^2", "@types/react-window": "^1.8.8", "dayjs": "^1.11.13", "react": "^18.3.1", "react-dom": "^18.3.1", "react-window": "^1.8.11", "recharts": "^2.15.4", "zod": "^3.25.39"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "rollup-plugin-visualizer": "^6.0.3", "ts-jest": "^29.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}