**Phase 1: Data Model & Core Logic Setup**

1.  **Define Data Types for Goals (`src/types/goal.ts` - New File)**
    *   [x] Create a new file `src/types/goal.ts`.
    *   [x] Define the `DailyGoal` interface:
          ```typescript
          export interface DailyGoal {
            targetAmount: number;
            currency: string; // e.g., "USD"
            isEnabled: boolean;
            lastNotifiedPercent: number; // Tracks 0, 50, 75, 100, or >100 to prevent re-notifications for the same tier on the same day
            lastNotificationDate: string; // YYYY-MM-DD, to reset lastNotifiedPercent daily
            createdAt: string; // ISO date
            updatedAt: string; // ISO date
          }
          ```
    *   [x] Define the `DailyGoalAchievement` interface:
          ```typescript
          export interface DailyGoalAchievement {
            id: string; // Unique ID for the achievement record
            date: string; // YYYY-MM-DD
            goalAmount: number;
            earnedAmount: number;
            currency: string;
            percentageAchieved: number; // e.g., 120 for 120%
            status: 'hit' | 'missed' | 'exceeded';
            difference: number; // Positive if exceeded/hit, negative if missed
            recordedAt: string; // ISO date
          }
          ```
    *   [x] (Optional but Recommended) Add Zod schemas for `DailyGoal` and `DailyGoalAchievement` if your project consistently uses Zod for validation (similar to `src/types/timer.ts`).

2.  **Update Storage Constants & Service (`src/constants/index.ts`, `src/services/StorageService.ts`)**
    *   [x] In `src/constants/index.ts`, add `STORAGE_KEYS.DAILY_GOAL` and `STORAGE_KEYS.DAILY_GOAL_ACHIEVEMENTS`.
    *   [x] In `src/services/StorageService.ts`:
        *   [x] Add `async getDailyGoal(): Promise<DailyGoal | null>` method.
        *   [x] Add `async setDailyGoal(goal: DailyGoal): Promise<void>` method.
        *   [x] Add `async getGoalAchievements(): Promise<DailyGoalAchievement[]>` method.
        *   [x] Add `async setGoalAchievements(achievements: DailyGoalAchievement[]): Promise<void>` method.
        *   [x] (Consider schema validation within these methods if Zod schemas were created).

3.  **Create `useDailyGoals` Hook (`src/hooks/useDailyGoals.ts` - New File)**
    *   [x] Create the file `src/hooks/useDailyGoals.ts`.
    *   [x] This hook should use `useLocalStorage` (or `StorageService`) to manage `DailyGoal` (single object) and `DailyGoalAchievement[]`.
    *   [x] Expose functions:
        *   `getCurrentGoal(): DailyGoal | null`
        *   `updateDailyGoal(goalData: Partial<Omit<DailyGoal, 'createdAt' | 'updatedAt' | 'lastNotifiedPercent' | 'lastNotificationDate'>>): Promise<void>` (handles `updatedAt`)
        *   `enableDailyGoal(isEnabled: boolean): Promise<void>`
        *   `getAchievementsForDateRange(startDate: string, endDate: string): Promise<DailyGoalAchievement[]>`
        *   `recordAchievement(achievementData: Omit<DailyGoalAchievement, 'id' | 'recordedAt'>): Promise<DailyGoalAchievement>` (generates ID, sets `recordedAt`)
        *   `updateNotificationState(percent: number, date: string): Promise<void>` (updates `lastNotifiedPercent` and `lastNotificationDate` in the `DailyGoal` object)

4.  **Earnings Calculation Utility (`src/utils/earningsCalculator.ts` - New File)**
    *   [x] Create `earningsCalculator.ts`.
    *   [x] Implement `calculateTotalEarningsForDate(date: string, timeEntries: TimeEntry[], tasks: Task[]): number`. This function will sum up earnings for completed tasks on a given day based on their duration and hourly rate.
        *   [x] Ensure it correctly handles tasks with and without `hourlyRate`.
        *   [x] Ensure it only considers `timeEntries` that have an `endTime` and `duration`.

**Phase 2: Goal Configuration UI**

1.  **Settings Page Integration (`src/components/pages/SettingsPage.tsx`)**
    *   [x] Add a new section: "Daily Earnings Goal".
    *   [x] Use `CurrencyInput` for `targetAmount`.
    *   [x] Use an MUI `Switch` for `isEnabled`.
    *   [x] Add a "Save Goal" button.
    *   [x] Utilize the `useDailyGoals` hook to fetch the current goal and to save changes.
    *   [x] Use `useNotification` for feedback on save (success/error).

**Phase 3: Progress Tracking & Notifications**

1.  **Create `useDailyGoalProgressMonitor` Hook (`src/hooks/useDailyGoalProgressMonitor.ts` - New File)**
    *   [x] Create `useDailyGoalProgressMonitor.ts`.
    *   [x] This hook will be responsible for monitoring daily progress and triggering notifications.
    *   [x] Dependencies: `useDailyGoals`, `timeEntries` (from `App.tsx` or context), `tasks` (from `useTaskManagement`).
    *   [x] `useEffect` to run when `timeEntries` or `currentGoal` (from `useDailyGoals`) changes.
    *   [x] Inside the effect:
        *   [x] If goal is not enabled or no `targetAmount`, return.
        *   [x] Get today's date (YYYY-MM-DD).
        *   [x] If `currentGoal.lastNotificationDate` is not today, call `updateNotificationState(0, today)` via `useDailyGoals` to reset `lastNotifiedPercent`.
        *   [x] Calculate current day's total earnings using `calculateTotalEarningsForDate`.
        *   [x] Calculate progress percentage: `(earnedAmount / targetAmount) * 100`.
        *   [x] Implement notification logic:
            *   [x] **50% Check:** If `progress >= 50` and `currentGoal.lastNotifiedPercent < 50`:
                *   [x] Send desktop notification: "Halfway to your goal! Keep up the great work!"
                *   [x] Call `updateNotificationState(50, today)`.
            *   [x] **75% Check:** If `progress >= 75` and `currentGoal.lastNotifiedPercent < 75`:
                *   [x] Send desktop notification: "You're 75% there! Almost at your daily goal!"
                *   [x] Call `updateNotificationState(75, today)`.
            *   [x] **100% Check:** If `progress >= 100` and `currentGoal.lastNotifiedPercent < 100`:
                *   [x] Send desktop notification: "Goal Achieved! Fantastic job today!"
                *   [x] Call `updateNotificationState(100, today)`.
            *   [x] **Exceeded Check:** If `progress > 100` and `currentGoal.lastNotifiedPercent <= 100` (or a specific threshold like 101 to avoid immediate double notification with 100%):
                *   [x] Send desktop notification: "Goal CRUSHED! You're on fire today!"
                *   [x] Call `updateNotificationState(101, today)` (or actual percent if preferred, just needs to be >100).
2.  **Desktop Notification Integration (within `useDailyGoalProgressMonitor.ts`)**
    *   [x] Import `sendNotification`, `isPermissionGranted`, `requestPermission` from `@tauri-apps/api/notification`.
    *   [x] Create a helper function `async function triggerDesktopNotification(title: string, body: string)`:
        *   [x] Check `window.__TAURI__` to ensure it's running in Tauri.
        *   [x] Call `isPermissionGranted()`. If not granted, call `requestPermission()`. If permission denied or not granted, log and return.
        *   [x] Call `sendNotification({ title, body })`.
3.  **Daily Achievement Recording (within `useDailyGoalProgressMonitor.ts` or `App.tsx`)**
    *   [ ] Implement logic to detect when a new day starts (e.g., in `App.tsx`'s main `useEffect` or a dedicated effect in `useDailyGoalProgressMonitor` that watches the current date).
    *   [ ] When a new day starts:
        *   Get the goal settings and earned amount for the *previous* day.
        *   If a goal was enabled and had a `targetAmount` for the previous day:
            *   Calculate final `earnedAmount`, `percentageAchieved`, `status`, `currency`, and `difference`.
            *   Call `recordAchievement` from `useDailyGoals` to save the `DailyGoalAchievement` for the previous day.
        *   Call `updateNotificationState(0, new_day_date_string)` to reset notification tracking for the new day.

**Phase 4: UI Display**

1.  **Dashboard Widget (`src/components/ui/display/DailyGoalProgressWidget.tsx` - New File)**
    *   [ ] Create `DailyGoalProgressWidget.tsx`.
    *   [ ] Props: `currentEarnings: number`, `dailyGoal: DailyGoal | null`.
    *   [ ] If `dailyGoal` is null or `!dailyGoal.isEnabled` or `!dailyGoal.targetAmount`, display "Daily goal not active."
    *   [ ] Otherwise, display:
        *   Text: "Today's Goal: `formatCurrency(currentEarnings)` / `formatCurrency(dailyGoal.targetAmount)`"
        *   MUI `LinearProgress` bar: `value={(currentEarnings / dailyGoal.targetAmount) * 100}`.
        *   Percentage text: `Math.round((currentEarnings / dailyGoal.targetAmount) * 100)}%`.
2.  **Dashboard Integration (`src/types/ui.ts`, `src/hooks/useDashboardSettings.ts`, `src/components/pages/DashboardPage.tsx`)**
    *   [ ] In `src/types/ui.ts`, add `'daily-goal-progress'` to `DashboardWidgetId`.
    *   [ ] In `src/hooks/useDashboardSettings.ts`, add the new widget to `DEFAULT_WIDGETS` with appropriate `title`, `description`, `defaultEnabled`, and `defaultOrder`.
    *   [ ] In `DashboardPage.tsx`:
        *   Use `useDailyGoals` to get `currentGoal`.
        *   Use `calculateTotalEarningsForDate` to get `currentEarnings` for today.
        *   In the `renderWidget` function, add a case for `'daily-goal-progress'` to render your new `DailyGoalProgressWidget`, passing the `currentEarnings` and `currentGoal`.
3.  **Reports Page - Goal Achievements (`src/components/pages/ReportsPage.tsx`, `src/components/ui/tables/GoalAchievementsTable.tsx` - New File)**
    *   [ ] Create `GoalAchievementsTable.tsx`. This table will display `DailyGoalAchievement` data.
        *   Columns: "Date", "Goal Set", "Earned", "% Achieved", "Status", "Difference".
        *   Use MUI `Chip` for "Status" with colors (e.g., green for hit/exceeded, red for missed).
        *   Format "Difference" with +/- and currency.
    *   [ ] In `ReportsPage.tsx`:
        *   Add a new section or tab for "Daily Goal Achievements".
        *   Use `useDailyGoals` to fetch `DailyGoalAchievement[]`.
        *   Implement date range filtering for these achievements.
        *   Render the `GoalAchievementsTable` with the filtered achievements.

**Phase 5: Application-Level Integration**

1.  **Integrate `useDailyGoalProgressMonitor` in `App.tsx` (or `AppContent.tsx`)**
    *   [ ] Instantiate the hook. It needs access to `timeEntries` (which `App.tsx` manages) and `tasks` (from `useTaskManagement`).
    *   This hook will run in the background of the app, listening for changes and triggering notifications.
2.  **Global State Management for Earnings (Refinement)**
    *   [ ] Consider if `currentDailyEarnings` should be a piece of global state or context if multiple components need it frequently and re-calculating is too slow. For now, `useDailyGoalProgressMonitor` can calculate it, and `DashboardPage` can also calculate it for its own display.

**Phase 6: Testing**
*   [ ] Unit Test `earningsCalculator.ts`.
*   [ ] Unit Test `useDailyGoals.ts` (mock `localStorage` or `StorageService`).
*   [ ] Unit Test `useDailyGoalProgressMonitor.ts` (mock dependencies, including Tauri notification calls).
*   [ ] Component tests for `DailyGoalProgressWidget.tsx` and `GoalAchievementsTable.tsx`.
*   [ ] Integration tests:
    *   Set a goal, log time entries, verify notifications are triggered.
    *   Verify dashboard widget updates correctly.
    *   Verify reports page shows achievement data accurately.
    *   Verify data persistence for goals and achievements.

